<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只允许POST请求');
    }

    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!isset($data['id'])) {
        throw new Exception('缺少故障记录ID');
    }

    $faultId = intval($data['id']);
    if ($faultId <= 0) {
        throw new Exception('无效的故障记录ID');
    }

    // 开始事务
    $conn->begin_transaction();

    // 首先检查故障记录是否存在
    $checkSql = "SELECT id, recorder FROM alarmlist WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    if (!$checkStmt) {
        throw new Exception("准备检查语句失败: " . $conn->error);
    }

    $checkStmt->bind_param('i', $faultId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    $fault = $result->fetch_assoc();

    if (!$fault) {
        throw new Exception("故障记录不存在");
    }

    // 权限检查：需要验证用户是否有删除权限
    // 这里可以添加session验证或其他权限检查逻辑
    // 暂时跳过权限检查，因为前端已经做了权限控制

    // 删除相关的附件记录
    $deleteFilesSql = "DELETE FROM fault_files WHERE fault_id = ?";
    $deleteFilesStmt = $conn->prepare($deleteFilesSql);
    if (!$deleteFilesStmt) {
        throw new Exception("准备删除附件语句失败: " . $conn->error);
    }

    $deleteFilesStmt->bind_param('i', $faultId);
    if (!$deleteFilesStmt->execute()) {
        throw new Exception("删除附件记录失败: " . $deleteFilesStmt->error);
    }

    // 删除主记录
    $deleteFaultSql = "DELETE FROM alarmlist WHERE id = ?";
    $deleteFaultStmt = $conn->prepare($deleteFaultSql);
    if (!$deleteFaultStmt) {
        throw new Exception("准备删除故障记录语句失败: " . $conn->error);
    }

    $deleteFaultStmt->bind_param('i', $faultId);
    if (!$deleteFaultStmt->execute()) {
        throw new Exception("删除故障记录失败: " . $deleteFaultStmt->error);
    }

    // 检查是否真的删除了记录
    if ($deleteFaultStmt->affected_rows === 0) {
        throw new Exception("未找到要删除的故障记录");
    }

    // 提交事务
    $conn->commit();

    // 尝试删除物理文件（如果有的话）
    // 这里可以添加删除上传文件的逻辑
    // 由于文件路径可能需要从fault_files表获取，而该表已被删除，
    // 所以物理文件删除需要在删除数据库记录之前进行

    echo json_encode([
        'success' => true,
        'message' => '故障记录已成功删除'
    ]);

} catch (Exception $e) {
    // 回滚事务
    if ($conn->ping()) {
        $conn->rollback();
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
