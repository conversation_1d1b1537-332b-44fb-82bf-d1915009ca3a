<?php
require_once 'db_config.php';

// 接收POST数据
$data = json_decode(file_get_contents("php://input"), true);
$account = $data['account'] ?? '';
$password = $data['password'] ?? '';

// 验证账号密码
$sql = "SELECT * FROM loginlist WHERE account = ? AND password = ? LIMIT 1";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $account, $password);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    // 设置session
    session_start();
    $_SESSION['user_account'] = $user['account'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_department'] = $user['department'];
    $_SESSION['user_section'] = $user['section'];
    $_SESSION['user_level'] = $user['level'];
    $_SESSION['user_splevel'] = $user['splevel'];
    $_SESSION['user_allevel'] = $user['allevel'];

    echo json_encode([
        'success' => true,
        'data' => [
            'account' => $user['account'],
            'name' => $user['name'],
            'department' => $user['department'],
            'section' => $user['section'],
            'level' => $user['level'],
            'splevel' => $user['splevel'],
            'allevel' => $user['allevel']
        ],
        'redirect' => 'index.html'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => '工号或密码错误'
    ]);
}

$stmt->close();
$conn->close();
?> 