/* 故障页面图表区域样式 */
.fault-charts {
  margin: 20px 0;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.faultchart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  min-width: 300px;
  padding: 0 10px;
  margin-bottom: 20px;
}

.faultchart-container h3 {
  text-align: center;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .faultchart-container {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.search-btn,
.reset-btn,
.download-btn,
.add-btn {
    padding: 6px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.search-btn,
.download-btn,
.add-btn {
    background-color: #4c6fff;
    color: white;
}

.reset-btn {
    background-color: #6c757d;
    color: white;
}

.search-btn:hover,
.download-btn:hover,
.add-btn:hover {
    background-color: #3a5ae8;
}

.reset-btn:hover {
    background-color: #5a6268;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-left: auto; 
}

/* 故障操作按钮样式 */
.fault-action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.fault-action-btn {
  width: 70px;
  height: 30px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #007bff;
  color: white;
}

.fault-action-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.fault-action-btn-danger {
  background-color: #dc3545;
}

.fault-action-btn-danger:hover {
  background-color: #c82333;
}

/* 加载弹框样式 */
.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-modal.show {
  opacity: 1;
  visibility: visible;
}

.loading-modal-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-width: 300px;
  max-width: 400px;
}

/* 加载动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4c6fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 成功提示样式 */
.success-icon {
  width: 60px;
  height: 60px;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  font-weight: bold;
  margin: 0 auto 20px;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 25px;
  font-weight: 500;
}

.success-confirm-btn {
  background-color: #4c6fff;
  color: white;
  border: none;
  padding: 10px 30px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.success-confirm-btn:hover {
  background-color: #3a5ae8;
}

/* 提交按钮禁用状态 */
.btn-submit:disabled {
  background-color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}