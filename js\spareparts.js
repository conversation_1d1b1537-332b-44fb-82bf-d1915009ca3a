let componentContext;
class NoticeManager {
    
    constructor() {
        this.initializeEventListeners();
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
        
        // 分页设置
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        
        // 请购列表分页设置
        this.needCurrentPage = 1;
        this.needPageSize = 10;
        this.needTotalPages = 1;
        
        // 搜索条件
        this.searchParams = {
            state: 'close',  // 默认加载库存状态
            section: this.userInfo?.section || '',
            key: '',
            location: '',
            code: ''
        };
    }

    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeButton();        // 初始化 按钮
            this.initializeTableRows();     // 初始化 备品列表 表格行
            this.initializeNeedTableRows(); // 初始化 备品请购 表格行
            this.loadSectionOptions();      // 加载科室选项
            this.loadNeedSectionOptions();
            this.bindSearchForm();          // 绑定搜索表单事件
            this.bindNeedSearchForm();      // 绑定请购搜索事件

            
            // 设置默认状态并加载数据
            this.loadSparepartsList();      // 加载备品列表数据
            this.loadSparepartsNeedList();  // 加载备品请购数据
            this.loadSparepartsHistoryList();//加载领用履历数据
            this.bindHistoryForm();         // 绑定历史表单事件
            this.initializePagination();    // 初始化分页控件
        });
    }
    
    initializeButton(){
        const spload = document.getElementById('spload');
        const sphistory = document.getElementById('sphistory');
        if(this.userInfo?.splevel === 1){
            spload.style="display:block";
            sphistory.style="display:block";
        }
        
    }

    // 加载科室选项
    async loadSectionOptions() {
        try {
            const sectionSelect = document.getElementById('sparepartssearch-section');
            if (sectionSelect) {
                const response = await fetch('php/get_options.php?type=section');
                const result = await response.json();
                
                if (result.success) {
                    sectionSelect.innerHTML = '<option value="">全部</option>' + 
                        result.data.map(section => `<option value="${section}">${section}</option>`).join('');
                    
                    // 如果用户有科室，默认选中
                    if (this.userInfo?.section) {
                        sectionSelect.value = this.userInfo.section;
                    }
                }
            }
        } catch (error) {
            console.error('加载科室选项失败:', error);
        }
    }

        // 加载请购科室选项
        async loadNeedSectionOptions() {
            try {
                const sectionSelect = document.getElementById('needsearch-section');
                if (sectionSelect) {
                    const response = await fetch('php/get_options.php?type=section');
                    const result = await response.json();
                    
                    if (result.success) {
                        sectionSelect.innerHTML = '<option value="">全部</option>' + 
                            result.data.map(section => `<option value="${section}">${section}</option>`).join('');

                    // 如果用户有科室，默认选中
                    if (this.userInfo?.section) {
                        sectionSelect.value = this.userInfo.section;
                        }
                    }
                }
            } catch (error) {
                console.error('加载科室选项失败:', error);
            }
        }
    
    // 绑定搜索表单事件
    bindSearchForm() {
        const searchForm = document.getElementById('sparepartssearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                // 获取搜索条件
                this.searchParams.state = document.getElementById('sparepartssearch-state').value;
                this.searchParams.section = document.getElementById('sparepartssearch-section').value;
                this.searchParams.key = document.getElementById('sparepartssearch-key').value;
                this.searchParams.location = document.getElementById('sparepartssearch-location').value;
                this.searchParams.code = document.getElementById('sparepartssearch-code').value;
                
                // 重置页码并加载数据
                this.currentPage = 1;
                this.loadSparepartsList();
                
                // 请购tab页更新请购列表
                if (document.getElementById('spneedTab').classList.contains('active')) {
                    this.loadSparepartsNeedList();
                }
                // 履历tab页更新请购列表
                if (document.getElementById('sphistoryTab').classList.contains('active')) {
                    this.loadSparepartsHistoryList();
                }
            });
            
            // 重置按钮事件
            const resetBtn = searchForm.querySelector('.sparepartsreset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // 重置所有搜索条件
                    document.getElementById('sparepartssearch-state').value = 'close';
                    document.getElementById('sparepartssearch-key').value = '';
                    document.getElementById('sparepartssearch-location').value = '';
                    document.getElementById('sparepartssearch-code').value = '';
                    
                    // 确保用户科室被选中
                    if (this.userInfo?.section) {
                        document.getElementById('sparepartssearch-section').value = this.userInfo.section;
                    }
                    
                    // 更新搜索参数
                    this.searchParams.state = 'close';
                    this.searchParams.section = this.userInfo?.section || '';
                    this.searchParams.key = '';
                    this.searchParams.location = '';
                    this.searchParams.code = '';
                    
                    // 重置页码并加载数据
                    this.currentPage = 1;
                    this.loadSparepartsList();
                    this.loadSparepartsNeedList();
                    this.loadSparepartsHistoryList();
                });
            }

            // 导出按钮事件
            const downloadAllBtn = searchForm.querySelector('.sparepartsdownload-btn');
            if (downloadAllBtn) {
                downloadAllBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.exportAllData();
                });
            }
        }
    }

    // 初始化 备品列表 表格行
    initializeTableRows() {
        const tbody = document.getElementById('splistTableBody');
        if (!tbody) return;
        
        // 清空现有行
        tbody.innerHTML = '';
        
        // 添加5行空表格
        for (let i = 0; i < 5; i++) {
            this.addNewRow(tbody);
        }

        // 绑定添加更多行按钮事件
        const addRowsBtn = document.querySelector('.btn-add-rows');

        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', () => {
                for (let i = 0; i < 5; i++) {
                    this.addNewRow(tbody);
                }
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.spload-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();//阻止连接默认的跳转行为
                this.submitForm(form);
            });
        }
    }

    // 添加新行
    addNewRow(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;
        
        row.innerHTML = `
            <td>
                <input type="text" name="name[]" required>
            </td>
            <td>
                <input type="text" name="model[]" required>
            </td>
            <td>
                <input type="text" name="location[]" required>
            </td>
            <td>
                <input type="text" name="code[]" required>
            </td>
            <td>
                <input type="text" name="use[]" required>
            </td>
            <td>
                <input type="number" name="quantity[]" min="1" required>
            </td>
            <td>
                <select id="spload-section-${rowIndex}" name="section[]">
                <option value="ALL">全部</option>
                <option value="偏贴实装一科">偏贴实装一科</option>
                <option value="偏贴实装二科">偏贴实装二科</option>
                <option value="偏贴实装三科">偏贴实装三科</option>
                <option value="检测一科">检测一科</option>
                <option value="检测二科">检测二科</option>
                <option value="中板切科">中板切科</option>
                </select>
            </td>
            <td>
                <input type="file" id="sploadFileUpload-${rowIndex}" name="spload_files[]" multiple 
                       accept="image/jpeg,image/png,image/gif" 
                       style="width: 100px;"
                       onchange="noticeManager.handleFileSelect(event, ${rowIndex})">
                <div class="file-list" id="sploadFileList-${rowIndex}"></div>
            </td>
            <td class="action-column">
                <button type="button" class="btn-delete-row" onclick="noticeManager.deleteRow(this)">
                    删除
                </button>
            </td>
        `;
        tbody.appendChild(row);
    }

    // 处理文件选择
    handleFileSelect(event, rowIndex) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`sploadFileList-${rowIndex}`);
        const fileInput = event.target;
        
        if (!fileListDiv) return;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = '';
            return;
        }

        fileListDiv.innerHTML = '';// 清空现有文件列表
        // 添加新文件到现有列表
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" 
                        onclick="noticeManager.removeFile(${rowIndex}, '${file.name}', this, 'spload')">×</button>
            `;
            fileListDiv.appendChild(fileItem);
        });        
    }
    
    // 移除文件
    removeFile(rowIndex, fileName, button, type) {
        const prefix = type === 'spload' ? 'spload' : 'spneed';
        const fileInput = document.querySelector(`input[name="${prefix}_files[]"][onchange*="${rowIndex}"]`);
        const fileListDiv = document.getElementById(`${prefix}FileList-${rowIndex}`);
        
        button.closest('.file-item').remove();
        
        if (fileListDiv.children.length === 0) {
            fileInput.value = '';
        }
    }

    // 提交表单
    async submitForm(form) {
        try {
            const formData = new FormData();
            let validRowCount = 0;  // 记录有效行数

            // 收集所有行的数据
            const tbody = document.getElementById('splistTableBody');
            const rows = tbody.children;
            for (let i = 0; i < rows.length; i++) {
                // 获取行中的所有输入值
                const name = rows[i].querySelector('input[name="name[]"]').value;
                const model = rows[i].querySelector('input[name="model[]"]').value;
                const location = rows[i].querySelector('input[name="location[]"]').value;
                const code = rows[i].querySelector('input[name="code[]"]').value;
                const use = rows[i].querySelector('input[name="use[]"]').value;
                const quantity = rows[i].querySelector('input[name="quantity[]"]').value;
                const section = rows[i].querySelector('select[name="section[]"]').value;
                // 只处理有必填字段的行
                if (name && model && location && code && use && quantity) {
                    // 添加行数据
                    formData.append('name', name);
                    formData.append('model', model);
                    formData.append('location', location);
                    formData.append('code', code);
                    formData.append('use', use);
                    formData.append('quantity', quantity);
                    formData.append('section', section);
                    // 添加当前用户作为recorder（确保不为空）
                    formData.append('recorder', this.userInfo.name);
                    
                    // 添加用户科室信息
                    //formData.append('section', this.userInfo.section);

                    // 添加备品状态
                    formData.append('state', 'close');

                    formData.append('reason', '');
                
                    // 添加文件（如果有）
                    const fileInput = rows[i].querySelector('input[name="spload_files[]"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            formData.append('files[]', file);
                        });
                    }
                    
                    try {
                        const response = await fetch('php/submit_spareparts.php', {
                            method: 'POST',
                            body: formData
                        });
    
                        const result = await response.json();
                        if (result.success) {
                            validRowCount++;
                        } else {
                            throw new Error(`第${i+1}行: ${result.message}`);
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        throw error;
                    }
                }
            }

            if (validRowCount > 0) {
                alert(`成功添加了 ${validRowCount} 条备品记录`);
                form.reset();
                this.initializeTableRows();
                window.location.href = 'spareparts.html';
            } else {
                alert('没有有效的备品数据被提交，请检查表单');
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }

    // 添加删除行的方法
    deleteRow(button) {
        const row = button.closest('tr');
        if (row) {
            // 获取tbody中的行数
            const tbody = row.parentElement;
            if (tbody.children.length > 1) {  // 确保至少保留一行
                row.remove();
            } else {
                alert('至少需要保留一行');
            }
        }
    }


    // 初始化 备品请购 表格行
    initializeNeedTableRows() {
        const tbody = document.getElementById('spneedTableBody');
        if (!tbody) return;
        
        // 清空现有行
        tbody.innerHTML = '';
        
        // 默认只添加1行
        this.addNewRowNeed(tbody);

        // 绑定添加更多行按钮事件
        const addRowsBtn = document.querySelector('#spneedTab .btn-add-rows');

        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', () => {
                // 每次只添加1行
                this.addNewRowNeed(tbody);
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.spneed-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitFormNeed(form);
            });
        }
    }
    
    // 添加新行
    addNewRowNeed(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;
        
        row.innerHTML = `
            <td>
                <input type="text" name="name[]" required>
            </td>
            <td>
                <input type="text" name="model[]" required>
            </td>
            <td>
                <input type="text" name="code[]">
            </td>
            <td>
                <input type="text" name="use[]" required>
            </td>
            <td>
                <input type="number" name="quantity[]" min="1" required>
            </td>
            <td>
                <input type="text" name="reason[]" required>
            </td>            
            <td>
                <input type="file" id="spneedFileUpload-${rowIndex}" name="spneed_files[]" multiple 
                       accept="image/jpeg,image/png,image/gif" 
                       style="width: 100px;"
                       onchange="noticeManager.handleFileSelectNeed(event, ${rowIndex})">
                <div class="file-list" id="spneedFileList-${rowIndex}"></div>
            </td>
            <td class="action-column">
                <button type="button" class="btn-delete-row" onclick="noticeManager.deleteRowNeed(this)">
                    删除
                </button>
            </td>
        `;
        tbody.appendChild(row);
    }
    
    // 处理文件选择
    handleFileSelectNeed(event, rowIndex) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`spneedFileList-${rowIndex}`);
        const fileInput = event.target;
        
        if (!fileListDiv) return;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = '';
            return;
        }
        fileListDiv.innerHTML = '';// 清空现有文件列表

        // 添加新文件到现有列表
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" 
                        onclick="noticeManager.removeFile(${rowIndex}, '${file.name}', this, 'spneed')">×</button>
            `;
            fileListDiv.appendChild(fileItem);
        });
    }

    // 提交表单
    async submitFormNeed(form) {
        try {
            const formData = new FormData();
            let validRowCount = 0;  // 记录有效行数

            // 收集所有行的数据
            const tbody = document.getElementById('spneedTableBody');
            const rows = tbody.children;
            for (let i = 0; i < rows.length; i++) {
                // 获取行中的所有输入值
                const name = rows[i].querySelector('input[name="name[]"]').value;
                const model = rows[i].querySelector('input[name="model[]"]').value;
                const code = rows[i].querySelector('input[name="code[]"]').value;
                const use = rows[i].querySelector('input[name="use[]"]').value;
                const quantity = rows[i].querySelector('input[name="quantity[]"]').value;
                const reason = rows[i].querySelector('input[name="reason[]"]').value;

                // 只处理有必填字段的行
                if (name && model && use && reason && quantity) {
                    // 添加行数据
                    formData.append('name', name);
                    formData.append('model', model);
                    formData.append('location', '/');
                    formData.append('code', code);
                    formData.append('use', use);
                    formData.append('quantity', quantity);
                    formData.append('reason', reason);
                    // 添加当前用户作为recorder（确保不为空）
                    formData.append('recorder', this.userInfo.name);
                    // 添加用户科室信息
                    formData.append('section', this.userInfo.section);
                    // 添加备品状态
                    formData.append('state', 'open');
                
                    // 添加文件（如果有）
                    const fileInput = rows[i].querySelector('input[name="spneed_files[]"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            formData.append('files[]', file);
                        });
                    }
                    
                    try {
                        const response = await fetch('php/submit_spareparts.php', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();
                        if (result.success) {
                            validRowCount++;
                        } else {
                            throw new Error(`第${i+1}行: ${result.message}`);
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        throw error;
                    }
                }
            }

            if (validRowCount > 0) {
                alert(`成功添加了 ${validRowCount} 条请购记录`);
                form.reset();
                this.initializeNeedTableRows();
                window.location.href = 'spareparts.html';
            } else {
                alert('没有有效的备品数据被提交，请检查表单');
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }

    // 添加删除行的方法
    deleteRowNeed(button) {
        const row = button.closest('tr');
        if (row) {
            // 获取tbody中的行数
            const tbody = row.parentElement;
            if (tbody.children.length > 1) {  // 确保至少保留一行
                row.remove();
            } else {
                alert('至少需要保留一行');
            }
        }
    }

    //查询请购列表
    bindNeedSearchForm() {
        const searchForm = document.getElementById('needsearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                // 获取搜索条件
                this.searchParams.section = document.getElementById('needsearch-section').value;
                this.searchParams.key = document.getElementById('needsearch-key').value;
                this.searchParams.code = document.getElementById('needsearch-code').value;
                // this.searchParams.state = document.getElementById('needsearch-state').value;
                

                
                // 请购tab页更新请购列表
                if (document.getElementById('spneedTab').classList.contains('active')) {
                    this.loadSparepartsNeedList();
                }
                // 履历tab页更新请购列表
                if (document.getElementById('sphistoryTab').classList.contains('active')) {
                    this.loadSparepartsHistoryList();
                }
            });
            
            // 重置按钮事件
            const resetBtn = searchForm.querySelector('.sparepartsreset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // 重置请购搜索条件
                    document.getElementById('needsearch-section').value = '';
                    document.getElementById('needsearch-key').value = '';
                    document.getElementById('needsearch-code').value = '';
                    // document.getElementById('needsearch-state').value = 'open,run,arrive';
                    document.getElementById('needsearch-state').value = '';
                    
                    // 更新搜索参数
                    this.searchParams.section = '';
                    this.searchParams.key = '';
                    this.searchParams.code = '';
                    // this.searchParams.state = 'open,run,arrive';
                    this.searchParams.state = '';
                    
                    // 重置页码并加载数据
                    this.loadSparepartsList();
                    this.loadSparepartsNeedList();
                    this.loadSparepartsHistoryList();
                });
            }
    
            // 导出按钮事件
            const downloadBtn = searchForm.querySelector('.sparepartsdownload-btn');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.exportNeedData();
                });
            }

            // 复制按钮事件
            const copyBtn = document.getElementById("copy");
            if (copyBtn) {
                copyBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.copyData();
                });
            }
        }
    }
    

    // 加载备品列表
    async loadSparepartsList() {
        try {
            const queryParams = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize,
                state: this.searchParams.state,
                section: this.searchParams.section,
                key: this.searchParams.key,
                location: this.searchParams.location,
                code: this.searchParams.code
            });
            
            const response = await fetch(`php/get_spareparts.php?${queryParams.toString()}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateSparePartsTable(result.data);
                this.updatePagination(result);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载备品列表失败:', error);
            alert('加载失败：' + error.message);
        }
    }

    // 更新备品表格
    updateSparePartsTable(spareparts) {
        const tbody = document.querySelector('#splistTab .data-table tbody');
        if (!tbody) return;

        if (!spareparts || spareparts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">暂无备品记录</td></tr>';
            return;
        }

        tbody.innerHTML = spareparts.map(sparepart => `
            <tr>
                <td>${sparepart.name}</td>
                <td>${sparepart.model}</td>
                <td>${sparepart.location}</td>
                <td>${sparepart.code}</td>
                <td>${sparepart.use}</td>
                <td>${sparepart.quantity}</td>
                <td>${sparepart.section}</td>
                <td>
                    <span class="status-badge status-${sparepart.state === 'close' ? 'active' : 'expired'}">
                    ${sparepart.state === 'close' ? '库存' : ''}</span>
                </td>
                <td>
                    <button class="btn-view" onclick="noticeManager.viewSparepart(${sparepart.id})">查看</button>
                    ${this.userInfo?.splevel === 1 ?
                        `<button class="btn-delete" onclick="noticeManager.deleteSparepart(${sparepart.id})">删除</button>` : 
                        ''}
                </td>
            </tr>
        `).join('');
    }

    // 复制数据
    async copyData() {
        // 获取表格的 HTML 内容
        var gethtml = document.getElementById("spneedlist-list").outerHTML;
    
        // 创建一个临时的容器来复制内容
        var tempDiv = document.createElement("div");
        tempDiv.innerHTML = gethtml;
        document.body.appendChild(tempDiv);
    
        // 选中临时容器的内容
        var range = document.createRange();
        range.selectNodeContents(tempDiv);
        var selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
    
        // 执行复制命令
        try {
            document.execCommand("copy");

            // const email = '<EMAIL>';
            // const subject = encodeURIComponent('表格数据');
            // const promptText = "表格内容已复制到剪贴板，请按 Ctrl+V 粘贴到此处：\n\n";
            // const body = encodeURIComponent(promptText);
            // const mailtoLink = `mailto:${email}?subject=${subject}&body=${body}`;
            // window.location.href = mailtoLink;
            // alert("表格已复制到剪贴板，邮件客户端已打开！\n请在邮件正文中按 Ctrl+V 粘贴表格内容。");
            
            alert("表格已复制到剪贴板，请在输入处按 Ctrl+V 粘贴表格内容。");
        } catch (error) {
            alert("复制失败：" + error.message);
        } finally {
            // 确保临时容器被移除
            document.body.removeChild(tempDiv);
            selection.removeAllRanges();
        }
    }


    // 导出全部数据
    async exportAllData() {
        try {
            const queryParams = new URLSearchParams({
                page: 1,
                page_size: 0,
                state: this.searchParams.state,
                section: this.searchParams.section,
                key: this.searchParams.key,
                location: this.searchParams.location,
                code: this.searchParams.code
            });

            const response = await fetch(`php/get_spareparts.php?${queryParams.toString()}`);
            const result = await response.json();
            
            if (!result.success) throw new Error(result.message);
            

            const ws = XLSX.utils.json_to_sheet(result.data.map(item => ({
                "备品名称": item.name,
                "型号": item.model,
                "库存位置": item.location,
                "料号": item.code,
                "使用位置": item.use,
                "数量": item.quantity,
                "科室": item.section,
                "状态": item.state === 'close' ? '库存' : '请购'
            })));

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "备品数据");
            XLSX.writeFile(wb, `备品列表_${new Date().toLocaleDateString()}.xlsx`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    }

    // 导出请购数据
    async exportNeedData() {
        try {
            const queryParams = new URLSearchParams({
                page: 1,
                page_size: 0,
                state: 'open,run,arrive',
                key: this.needSearchParams?.key || '',
                code: this.needSearchParams?.code || ''
            });

            const response = await fetch(`php/get_spareparts.php?${queryParams.toString()}`);
            const result = await response.json();
            
            if (!result.success) throw new Error(result.message);

            const ws = XLSX.utils.json_to_sheet(result.data.map(item => ({
                "备品名称": item.name,
                "型号": item.model,
                "库存位置": item.location,
                "料号": item.code,
                "使用位置": item.use,
                "请购数量": item.quantity,
                "科室": item.section,
                "状态": item.state === 'open' ? '请购' : 
                    item.state === 'run' ? '请购中' : 
                    item.state === 'arrive' ? '已到货' : '其他'
            })));

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "请购数据");
            XLSX.writeFile(wb, `备品请购列表_${new Date().toLocaleDateString()}.xlsx`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    }


    // 初始化分页控件
    initializePagination() {
        // 页码大小变化
        const pageSizeSelect = document.querySelector('#splistTab .page-size');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.loadSparepartsList();
            });
        }
        
        const needPageSizeSelect = document.querySelector('#spneedTab .page-size');
        if (needPageSizeSelect) {
            needPageSizeSelect.addEventListener('change', (e) => {
                this.needPageSize = parseInt(e.target.value);
                this.needCurrentPage = 1;
                this.loadSparepartsNeedList();
            });
        }
        
        // 页码导航按钮 
        document.querySelector('#splistTab .btn-prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadSparepartsList();
            }
        });
        
        document.querySelector('#splistTab .btn-next-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadSparepartsList();
            }
        });
        
        document.querySelector('#spneedTab .btn-prev-page')?.addEventListener('click', () => {
            if (this.needCurrentPage > 1) {
                this.needCurrentPage--;
                this.loadSparepartsNeedList();
            }
        });
        
        document.querySelector('#spneedTab .btn-next-page')?.addEventListener('click', () => {
            if (this.needCurrentPage < this.needTotalPages) {
                this.needCurrentPage++;
                this.loadSparepartsNeedList();
            }
        });
        
        // 首页和末页 
        document.querySelector('#splistTab .btn-first-page')?.addEventListener('click', () => {
            this.currentPage = 1;
            this.loadSparepartsList();
        });
        
        document.querySelector('#splistTab .btn-last-page')?.addEventListener('click', () => {
            this.currentPage = this.totalPages;
            this.loadSparepartsList();
        });
        
        document.querySelector('#spneedTab .btn-first-page')?.addEventListener('click', () => {
            this.needCurrentPage = 1;
            this.loadSparepartsNeedList();
        });
        
        document.querySelector('#spneedTab .btn-last-page')?.addEventListener('click', () => {
            this.needCurrentPage = this.needTotalPages;
            this.loadSparepartsNeedList();
        });
        
        // 页码输入框 
        const pageInput = document.querySelector('#splistTab .current-page');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.loadSparepartsList();
                } else {
                    e.target.value = this.currentPage;
                }
            });
        }
        
        const needPageInput = document.querySelector('#spneedTab .current-page');
        if (needPageInput) {
            needPageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= this.needTotalPages) {
                    this.needCurrentPage = page;
                    this.loadSparepartsNeedList();
                } else {
                    e.target.value = this.needCurrentPage;
                }
            });
        }
    }

    // 更新分页信息
    updatePagination(result) {
        this.totalPages = result.totalPages;
        
        document.querySelector('.total-count').textContent = result.total;
        document.querySelector('.total-pages').textContent = result.totalPages;
        document.querySelector('.current-page').value = result.currentPage;
    }

    // 更新请购分页信息
    updateNeedPagination(result) {
        this.needTotalPages = result.totalPages;
        
        const needPagination = document.querySelector('#spneedTab .pagination');
        if (needPagination) {
            needPagination.querySelector('.total-count').textContent = result.total;
            needPagination.querySelector('.total-pages').textContent = result.totalPages;
            needPagination.querySelector('.current-page').value = result.currentPage;
        }
    }

    // 查看备品详情
    async viewSparepart(id) {
        try {
            // 获取备品基本信息
            const response = await fetch(`php/get_sparepart_detail.php?id=${id}`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message);
            }
            
            // 获取备品履历数据
            const historyResponse = await fetch(`php/get_sparepart_history.php?sparepart_id=${id}`);
            const historyResult = await historyResponse.json();
            
            if (!historyResult.success) {
                throw new Error(historyResult.message);
            }
            
            // 合并数据
            const sparepart = result.data;
            sparepart.history = historyResult.data;
            
            // 显示备品详情
            this.showSparepartDetail(sparepart);
        } catch (error) {
            console.error('获取备品详情失败:', error);
            alert('获取备品详情失败：' + error.message);
        }
    }

    // 显示备品详情
    showSparepartDetail(sparepart) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
        modal.style.zIndex = '1000';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';

        // 模态框内容
        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                padding: 20px;
                border-radius: 5px;
                width: 90%;
                max-width: 1400px;
                max-height: 90vh;
                overflow-y: auto;
            ">
                <span class="close-btn" style="
                    float: right;
                    font-size: 24px;
                    cursor: pointer;
                ">&times;</span>
                <h2>备品详情</h2>
                
                <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                    <!-- 左侧：备品详情表格 -->
                    <div style="flex: 0.7; min-width: 300px;">
                        <table class="detail-table" style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">备品名称:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="name">${sparepart.name}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">型号:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="model">${sparepart.model}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">库存位置:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="location">${sparepart.location}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">料号:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="code">${sparepart.code}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">使用位置:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="use">${sparepart.use}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">数量:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="quantity">${sparepart.quantity}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">记录人:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;">${sparepart.recorder}</td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">科室:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="section">
                                    <span class="section-text">${sparepart.section}</span>
                                    <select class="section-select" style="display: none; width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="ALL" ${sparepart.section === 'ALL' ? 'selected' : ''}>ALL</option>
                                        <option value="偏贴实装一科" ${sparepart.section === '偏贴实装一科' ? 'selected' : ''}>偏贴实装一科</option>
                                        <option value="偏贴实装二科" ${sparepart.section === '偏贴实装二科' ? 'selected' : ''}>偏贴实装二科</option>
                                        <option value="偏贴实装三科" ${sparepart.section === '偏贴实装三科' ? 'selected' : ''}>偏贴实装三科</option>
                                        <option value="检测一科" ${sparepart.section === '检测一科' ? 'selected' : ''}>检测一科</option>
                                        <option value="检测二科" ${sparepart.section === '检测二科' ? 'selected' : ''}>检测二科</option>
                                        <option value="中板切科" ${sparepart.section === '中板切科' ? 'selected' : ''}>中板切科</option>
                                    </select>
                                
                                </td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">状态:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;" data-field="state">
                                    <span class="state-text">${sparepart.state === 'close' ? '库存' : (sparepart.state === 'run' ? '请购中' : (sparepart.state === 'arrive' ? '已到货' : '请购'))}</span>
                                    <select class="state-select" style="display: none; width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    ${this.userInfo?.splevel === 1 ?
                                        `<option value="close" ${sparepart.state === 'close' ? 'selected' : ''}>库存</option>
                                        <option value="run" ${sparepart.state === 'run' ? 'selected' : ''}>请购中</option>
                                        <option value="arrive" ${sparepart.state === 'arrive' ? 'selected' : ''}>已到货</option>` : 
                                        ''}
                                        <option value="open" ${sparepart.state === 'open' ? 'selected' : ''}>请购</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th style="text-align: right; padding: 8px; border: 1px solid #ddd;">添加时间:</th>
                                <td style="padding: 8px; border: 1px solid #ddd;">${sparepart.created_at}</td>
                            </tr>
                        </table>
                        
                        <div class="files-container" style="margin-top: 20px;">
                            <h3>附件列表</h3>
                            ${sparepart.files && sparepart.files.length > 0 ? 
                                `<div class="files-list">
                                    ${sparepart.files.map(file => {
                                        const isImage = file.file_type && file.file_type.startsWith('image/');
                                        if (isImage) {
                                            // 确保文件路径正确编码
                                            const encodedPath = encodeURIComponent(file.file_path);
                                            
                                            // 检查文件是否存在
                                            if (file.file_exists === false) {
                                                return `
                                                    <div style="margin: 10px 0;">
                                                        <div style="padding: 20px; background-color: #f8f8f8; border: 1px dashed #ccc; text-align: center; color: #888;">
                                                            图片文件不存在: ${file.file_name}
                                                        </div>
                                                    </div>`;
                                            }
                                            
                                            return `
                                                <div style="margin: 10px 0; position: relative;">
                                                    <img src="uploads/spareparts/${encodedPath}" 
                                                         alt="${file.file_name}"
                                                         title="${file.file_name}"
                                                         style="
                                                            max-width: 100%;
                                                            max-height: 300px;
                                                            object-fit: contain;
                                                            cursor: pointer;
                                                         "
                                                         onerror="this.onerror=null; this.style.display='none'; const errorDiv = document.createElement('div'); errorDiv.style.padding = '20px'; errorDiv.style.backgroundColor = '#f8f8f8'; errorDiv.style.border = '1px dashed #ccc'; errorDiv.style.textAlign = 'center'; errorDiv.style.color = '#888'; errorDiv.innerText = '图片加载失败'; this.parentElement.insertBefore(errorDiv, this.nextSibling);"
                                                    >
                                                    <div style="text-align: center; margin-top: 5px; display: flex; justify-content: center; align-items: center;">
                                                        <span style="margin-right: 10px;">${file.file_name}</span>
                                                        ${this.userInfo?.splevel === 1 ? 
                                                            `<button onclick="noticeManager.deleteFile(${sparepart.id}, '${file.file_path}')" 
                                                                     style="
                                                                        padding: 2px 8px;
                                                                        background-color: #ff4444;
                                                                        color: white;
                                                                        border: none;
                                                                        border-radius: 4px;
                                                                        cursor: pointer;
                                                                        font-size: 12px;
                                                                     ">删除</button>` : 
                                                            ''}
                                                    </div>
                                                </div>`;
                                        } else {
                                            return `
                                                <div style="
                                                    background: #f5f5f5;
                                                    padding: 10px;
                                                    margin: 10px 0;
                                                    border-radius: 3px;
                                                    display: flex;
                                                    justify-content: space-between;
                                                    align-items: center;
                                                ">
                                                    <div style="cursor: pointer;" onclick="window.open('uploads/spareparts/${encodeURIComponent(file.file_path)}', '_blank')">
                                                        <span>${file.file_name}</span>
                                                        <span style="color: #666; margin-left: 10px;">
                                                            (${(file.file_size / 1024).toFixed(2)} KB)
                                                        </span>
                                                    </div>
                                                    ${this.userInfo?.splevel === 1 ? 
                                                        `<button onclick="noticeManager.deleteFile(${sparepart.id}, '${file.file_path}')"
                                                                 style="
                                                                    padding: 2px 8px;
                                                                    background-color: #ff4444;
                                                                    color: white;
                                                                    border: none;
                                                                    border-radius: 4px;
                                                                    cursor: pointer;
                                                                    font-size: 12px;
                                                                 ">删除</button>` : 
                                                        ''}
                                                </div>`;
                                        }
                                    }).join('')}
                                </div>` : 
                                '<div><p>无附件</p></div>'
                            }
                            ${this.userInfo?.splevel === 1 ||(sparepart.state === 'open' && this.userInfo?.name===sparepart.recorder)? 
                                `<div style="margin-top: 10px;">
                                    <input type="file" id="fileUpload-${sparepart.id}" 
                                           multiple 
                                           accept="image/jpeg,image/png,image/gif"
                                           style="display: none;"
                                           onchange="noticeManager.handleDetailFileSelect(event, ${sparepart.id})">
                                    <button onclick="document.getElementById('fileUpload-${sparepart.id}').click()" 
                                            style="
                                                padding: 8px 15px;
                                                background-color: #4c6fff;
                                                color: white;
                                                border: none;
                                                border-radius: 4px;
                                                cursor: pointer;
                                                font-size: 14px;
                                            ">
                                        添加文件
                                    </button>
                                    <div id="fileList-${sparepart.id}" class="file-list"></div>
                                </div>` : 
                                ''}
                        </div>
                    </div>
                    
                    <!-- 右侧：备品履历表格 -->
                    <div style="flex: 1.3; min-width: 300px;">
                        <!-- 标题和按钮在同一行 -->
                        <div style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 15px;
                        ">
                            <h3 style="margin: 0;">备品履历</h3>
                            <div style="display: flex; gap: 10px;">
                                <!-- 添加履历按钮 -->
                                ${sparepart.state === 'close' ?
                                    `<button id="addHistoryBtn" class="btn-add-history" style="
                                    padding: 8px 15px;
                                    background-color: #4c6fff;
                                    color: white;
                                    border: none;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    ">添加履历</button>`:
                                    ''}

                                <!-- 修改信息按钮 -->
                                ${this.userInfo?.splevel === 1 ||(sparepart.state === 'open' && this.userInfo?.name===sparepart.recorder)?
                                    `<button id="updateBtn" class="btn-update" style="
                                    padding: 8px 15px;
                                    background-color: #4c6fff;
                                    color: white;
                                    border: none;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                ">修改信息</button>` :
                                    ''}

                                <!-- 提交按钮（默认隐藏） -->
                                <button id="submitBtn" class="btn-submit" style="
                                    padding: 8px 15px;
                                    background-color: #4c6fff;
                                    color: white;
                                    border: none;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    display: none;
                                ">提交</button>
                            </div>
                        </div>
                        
                        <table class="history-table" style="text-align: center; width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                        <colgroup>
                            <col style="width: 10%">  <!-- 动作列 -->
                            <col style="width: 10%">  <!-- 数量列 -->
                            <col style="width: 10%">  <!-- 库存列 -->
                            <col style="width: 35%">  <!-- 使用位置列（加宽） -->
                            <col style="width: 10%">  <!-- 操作者列 -->
                            <col style="width: 15%">  <!-- 日期列 -->
                        </colgroup>
                            <thead>
                                <tr>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">变更动作</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">变更数量</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">库存数量</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">使用位置</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">操作者</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; background-color: #f2f2f2;">日期</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${sparepart.history && sparepart.history.length > 0 
                                    ? sparepart.history.map(item => `
                                        <tr>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${item.action}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd; ${item.quantity_change > 0 ? 'color: green;' : 'color: red;'}">${item.quantity_change}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${item.stock_quantity}</td>
                                            ${item.use_location ? `<td style="padding: 8px; border: 1px solid #ddd;">${item.use_location}</td>` : '<td style="padding: 8px; border: 1px solid #ddd;"></td>'}
                                            <td style="padding: 8px; border: 1px solid #ddd;">${item.operator}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">${item.formatted_date || item.change_date}</td>
                                        </tr>
                                    `).join('')
                                    : `<tr>
                                        <td colspan="6" style="text-align: center; padding: 10px; border: 1px solid #ddd;">
                                            暂无履历记录
                                        </td>
                                       </tr>`
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 关闭按钮事件
        modal.querySelector('.close-btn').onclick = () => {
            modal.remove();
        };

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function closeOnEsc(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', closeOnEsc);
            }
        });

        // 添加履历按钮事件
        const addHistoryBtn = modal.querySelector('#addHistoryBtn');
        if (addHistoryBtn) {
            addHistoryBtn.addEventListener('click', () => {
                this.showAddHistoryForm(sparepart.id, modal);
            });
        }
        
        // 修改信息按钮事件
        const updateBtn = modal.querySelector('#updateBtn');
        const submitBtn = modal.querySelector('#submitBtn');
        if (updateBtn && submitBtn) {
            updateBtn.addEventListener('click', () => {
                // 使表格单元格可编辑
                const editableCells = modal.querySelectorAll('td[data-field]');
                editableCells.forEach(cell => {
                    const fieldName = cell.getAttribute('data-field');
                    
                    if (sparepart.state === 'close') {
                        // 当状态为库存时，不允许修改数量和状态
                        if (fieldName !== 'state' && fieldName !== 'quantity') {
                            if (fieldName === 'section'){
                                cell.querySelector('.section-text').style.display = 'none';
                                cell.querySelector('.section-select').style.display = 'block';
                            } else {
                                cell.contentEditable = true;
                                cell.style.backgroundColor = '#f8f8f8';
                                cell.style.border = '1px solid #4c6fff';
                            }
                        }
                    } else {
                        // 当状态为请购时，所有字段都可编辑
                        if (fieldName === 'state') {
                            // 对于状态字段，显示下拉框并隐藏文本
                            cell.querySelector('.state-text').style.display = 'none';
                            cell.querySelector('.state-select').style.display = 'block';
                        } else if (fieldName === 'section'){
                            cell.querySelector('.section-text').style.display = 'none';
                            cell.querySelector('.section-select').style.display = 'block';
                        } else {
                            cell.contentEditable = true;
                            cell.style.backgroundColor = '#f8f8f8';
                            cell.style.border = '1px solid #4c6fff';
                        }
                    }
                });
                
                // 显示提交按钮，隐藏修改按钮
                updateBtn.style.display = 'none';
                submitBtn.style.display = 'inline-block';
            });
            
            submitBtn.addEventListener('click', async () => {
                try {
                    // 收集修改后的数据
                    const formData = new FormData();
                    formData.append('id', sparepart.id);
                    
                    const editableCells = modal.querySelectorAll('td[data-field]');
                    editableCells.forEach(cell => {
                        const fieldName = cell.getAttribute('data-field');
                        if (fieldName === 'state') {
                            // 对于状态字段，获取下拉框的值
                            const stateValue = cell.querySelector('.state-select').value;
                            formData.append(fieldName, stateValue);
                        } else if (fieldName === 'section') {
                            // 对于科室字段，获取下拉框的值
                            const sectionValue = cell.querySelector('.section-select').value;
                            formData.append(fieldName, sectionValue);
                        } else {
                            formData.append(fieldName, cell.textContent.trim());
                        }
                    });
                    
                    // 发送更新请求
                    const response = await fetch('php/update_sparepart.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        alert('修改成功');
                        // 关闭当前模态框并重新加载备品详情
                        modal.remove();
                        this.viewSparepart(sparepart.id);
                        // 刷新列表
                        this.loadSparepartsList();
                        this.loadSparepartsNeedList();
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    console.error('修改失败:', error);
                    alert('修改失败: ' + error.message);
                }
            });
        }
        
        // 为所有图片添加点击事件
        modal.querySelectorAll('.files-list img').forEach(img => {
            img.removeAttribute('onclick'); // 移除inline的onclick
            img.addEventListener('click', () => {
                this.showImagePreview(img.src, img.alt);
            });
        });
    }
    
    // 显示图片预览
    showImagePreview(src, alt) {
        // 创建图片预览模态框
        const imageModal = document.createElement('div');
        imageModal.className = 'image-preview-modal';
        imageModal.style.position = 'fixed';
        imageModal.style.top = '0';
        imageModal.style.left = '0';
        imageModal.style.width = '100%';
        imageModal.style.height = '100%';
        imageModal.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        imageModal.style.zIndex = '1200';
        imageModal.style.display = 'flex';
        imageModal.style.justifyContent = 'center';
        imageModal.style.alignItems = 'center';
        
        imageModal.innerHTML = `
            <div class="image-container" style="position: relative; max-width: 90%; max-height: 90%;">
                <img src="${src}" alt="${alt}" 
                     style="max-width: 100%; max-height: 90vh; object-fit: contain;"
                     onerror="this.style.display='none'; const errorMsg = document.createElement('div'); errorMsg.style.color='white'; errorMsg.style.padding='20px'; errorMsg.style.textAlign='center'; errorMsg.innerText='图片加载失败'; this.parentElement.appendChild(errorMsg);">
                <div style="color: white; text-align: center; margin-top: 10px; font-size: 16px;">${alt}</div>
                <button class="close-image-btn" style="
                    position: absolute;
                    top: -40px;
                    right: 0;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 30px;
                    cursor: pointer;
                ">&times;</button>
            </div>
        `;
        
        document.body.appendChild(imageModal);
        
        // 关闭按钮事件
        imageModal.querySelector('.close-image-btn').onclick = () => {
            imageModal.remove();
        };
        
        // 点击背景关闭
        imageModal.addEventListener('click', (e) => {
            if (e.target === imageModal) {
                imageModal.remove();
            }
        });
        
        // ESC键关闭
        const closeOnEsc = (e) => {
            if (e.key === 'Escape') {
                imageModal.remove();
                document.removeEventListener('keydown', closeOnEsc);
            }
        };
        document.addEventListener('keydown', closeOnEsc);
    }
    
    // 显示添加履历表单
    showAddHistoryForm(sparepartId, parentModal) {
        // 创建添加履历的表单模态框
        const historyFormModal = document.createElement('div');
        historyFormModal.className = 'history-form-modal';
        historyFormModal.style.position = 'fixed';
        historyFormModal.style.top = '0';
        historyFormModal.style.left = '0';
        historyFormModal.style.width = '100%';
        historyFormModal.style.height = '100%';
        historyFormModal.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        historyFormModal.style.zIndex = '1100';
        historyFormModal.style.display = 'flex';
        historyFormModal.style.justifyContent = 'center';
        historyFormModal.style.alignItems = 'center';
        
        historyFormModal.innerHTML = `
            <div class="form-content" style="
                background: white;
                padding: 20px;
                border-radius: 5px;
                width: 90%;
                max-width: 500px;
            ">
                <span class="close-form-btn" style="
                    float: right;
                    font-size: 24px;
                    cursor: pointer;
                ">&times;</span>
                <h3>添加备品履历</h3>
                <form id="addHistoryForm">
                    <input type="hidden" name="sparepart_id" value="${sparepartId}">
                    
                    <div style="margin-bottom: 15px;">
                        <label for="action" style="display: block; margin-bottom: 5px;">变更动作:</label>
                        <select id="action" name="action" style="width:100%; margin-right:4px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                            <option value="">请选择...</option>
                            
                            <option value="领用">领用</option>
                            ${this.userInfo?.splevel === 1 ? 
                                `<option value="入库">入库</option>` : 
                                ''}
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="quantity_change" style="display: block; margin-bottom: 5px;">变更数量:</label>
                        <input type="number" id="quantity_change" name="quantity_change" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div id="usageLocationContainer" style="margin-bottom: 15px; display: none;">
                        <label for="usage_location" style="display: block; margin-bottom: 5px;">使用位置:</label>
                        <input type="text" id="usage_location" name="usage_location" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"required>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="operator" style="display: block; margin-bottom: 5px;">操作者:</label>
                        <input type="text" id="operator" name="operator" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" readonly>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="date" style="display: block; margin-bottom: 5px;">日期:</label>
                        <input type="date" id="date" name="date" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="
                            padding: 8px 20px;
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        ">提交</button>
                        <button type="button" class="cancel-btn" style="
                            padding: 8px 20px;
                            background-color: #f44336;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">取消</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(historyFormModal);
        
        // 设置当前日期为默认值
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date').value = today;
        
        // 自动填充当前用户为操作者
        if (this.userInfo && this.userInfo.name) {
            document.getElementById('operator').value = this.userInfo.name;
        }

        // 自动填充使用位置
        if (document.getElementById('action').value === '入库') {
            document.getElementById('use_location').value = '';
        }
        
        // 变更动作改变时自动设置数量正负号
        const actionSelect = document.getElementById('action');
        const quantityInput = document.getElementById('quantity_change');
        const usageLocationContainer = document.getElementById('usageLocationContainer');
        const usageLocationInput = document.getElementById('usage_location');
        
        actionSelect.addEventListener('change', () => {
            // 显示/隐藏使用位置输入框
            if (actionSelect.value === '领用') {
                usageLocationContainer.style.display = 'block';
                usageLocationInput.required = true;  // 设为必填项
            } else {
                usageLocationContainer.style.display = 'none';
                usageLocationInput.required = false; // 取消必填
            }
            
            //数量处理逻辑
            if (actionSelect.value === '入库') {
                quantityInput.setAttribute('min', '1');
                quantityInput.value = Math.abs(quantityInput.value || 1);
            } else if (actionSelect.value === '领用') {
                quantityInput.setAttribute('min', '1');
                quantityInput.value = Math.abs(quantityInput.value || 1);
            }
        });
        
        // 关闭按钮事件
        historyFormModal.querySelector('.close-form-btn').onclick = () => {
            historyFormModal.remove();
        };
        
        // 取消按钮事件
        historyFormModal.querySelector('.cancel-btn').onclick = () => {
            historyFormModal.remove();
        };
        
        // 提交表单事件
        const form = historyFormModal.querySelector('#addHistoryForm');
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(form);
            
            // 根据动作类型自动设置数量正负号
            const action = formData.get('action');
            let quantityChange = parseInt(formData.get('quantity_change'));
            
            if (action === '领用' && quantityChange > 0) {
                quantityChange = -quantityChange;
            }
            
            formData.set('quantity_change', quantityChange.toString());
            
            try {
                // 发送请求到服务器
                const response = await fetch('php/add_sparepart_history.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('添加履历成功');
                    
                    // 关闭所有模态框
                    historyFormModal.remove();
                    parentModal.remove(); // 关闭原来的详情模态框
                    
                    // 刷新备品详情
                    this.viewSparepart(sparepartId);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('添加履历失败:', error);
                alert('添加履历失败: ' + error.message);
            }
        });
    }

    // 获取备品状态
    getSparepartsStatus(state) {
        if (state === 'close') {
            return { text: '库存', class: 'status-active' };
        } else {
            return { text: '请购', class: 'status-expired' };
        }
    }

    // 删除备品
    async deleteSparepart(id) {
        if (!confirm('确定要删除这条备品记录吗？')) {
            return;
        }

        try {
            const response = await fetch('php/delete_sparepart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id })
            });

            const result = await response.json();
            if (result.success) {
                alert('删除成功');
                this.loadSparepartsList();
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败：' + error.message);
        }
    }

    // 处理详情页面的文件选择
    async handleDetailFileSelect(event, sparepartId) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`fileList-${sparepartId}`);
        const fileInput = event.target;
        
        if (!fileListDiv) return;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = '';
            return;
        }

        try {
            const formData = new FormData();
            formData.append('id', sparepartId);
            
            // 添加所有文件
            files.forEach(file => {
                formData.append('files[]', file);
            });

            // 发送文件到服务器
            const response = await fetch('php/update_sparepart_files.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('文件上传成功');
                
                // 找到并关闭当前打开的模态框
                const currentModal = document.querySelector('.modal');
                if (currentModal) {
                    currentModal.remove();
                }
                
                // 重新加载备品详情
                this.viewSparepart(sparepartId);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('文件上传失败:', error);
            alert('文件上传失败: ' + error.message);
        }
    }

    // 加载备品请购列表
    async loadSparepartsNeedList() {
        try {
            const selectedState = document.getElementById('needsearch-state').value;
            const stateParam = selectedState === "" ? "open,run,arrive" : selectedState;
            const queryParams = new URLSearchParams({
                page: this.needCurrentPage,
                page_size: this.needPageSize,
                // page: 1,
                // page_size: 0,
                section:this.searchParams.section,
                // state: this.searchParams.state,
                state: stateParam,
                // state: 'open,run,arrive',
                key: this.searchParams.key,
                code: this.searchParams.code
            });
            
            const response = await fetch(`php/get_spareparts.php?${queryParams.toString()}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateSparePartsTableNeed(result.data);
                this.updateNeedPagination(result);
                const stateCounts = result.state_counts || { 
                    open: 0, 
                    run: 0, 
                    arrive: 0 
                };

                // const stateCounts = result.data.reduce((acc, item) => {
                //     acc[item.state] = (acc[item.state] || 0) + 1;
                //     return acc;
                // }, { open: 0, run: 0, arrive: 0 });

                const countLabel = document.getElementById('needsearch-count');
                if (countLabel) {
                    countLabel.innerHTML = 
                    `统计：待处理${stateCounts.open}个，请购中${stateCounts.run}个，已到货${stateCounts.arrive}个`
            }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载备品请购列表失败:', error);
            alert('加载失败：' + error.message);
        }
    }

    // 更新备品请购表格
    updateSparePartsTableNeed(spareparts) {
        const tbody = document.querySelector('#spneedTab .data-table tbody');
        if (!tbody) return;

        if (!spareparts || spareparts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" style="text-align: center;">暂无请购记录</td></tr>';
            return;
        }

        tbody.innerHTML = spareparts.map(sparepart => `
            <tr>
                <td>${sparepart.name}</td>
                <td>${sparepart.model}</td>
                <td>${sparepart.location}</td>
                <td>${sparepart.code}</td>
                <td>${sparepart.use}</td>
                <td>${sparepart.quantity}</td>
                <td>${sparepart.reason}</td>
                <td>${sparepart.section}</td>
                <td>${sparepart.recorder}</td>
                <td>${this.formatDate(sparepart.created_at)}</td>
                <td>
                    <span class="status-badge status-${sparepart.state === 'close' ? 'active' : (sparepart.state === 'run' ? 'running' : (sparepart.state === 'arrive' ? 'active' : 'expired'))}">
                    ${sparepart.state === 'close' ? '库存' : (sparepart.state === 'run' ? '请购中' : (sparepart.state === 'arrive' ? '已到货' : '请购'))}</span>
                </td>                
                <td>
                    <button class="btn-view" onclick="noticeManager.viewSparepart(${sparepart.id})">查看</button>
                    ${this.userInfo?.splevel === 1 ?
                        `<button class="btn-delete" onclick="noticeManager.deleteSparepart(${sparepart.id})">删除</button>` : 
                        ''}
                </td>
            </tr>
        `).join('');
    }

    // 删除文件
    async deleteFile(sparepartId, filePath) {
        if (!confirm('确定要删除这个文件吗？')) {
            return;
        }

        try {
            const response = await fetch('php/delete_sparepart_file.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sparepart_id: sparepartId,
                    file_path: filePath
                })
            });

            const result = await response.json();
            if (result.success) {
                alert('文件删除成功');
                
                // 找到并关闭当前打开的模态框
                const currentModal = document.querySelector('.modal');
                if (currentModal) {
                    currentModal.remove();
                }
                
                // 重新加载备品详情
                this.viewSparepart(sparepartId);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('文件删除失败:', error);
            alert('文件删除失败: ' + error.message);
        }
    }

    // 加载领用履历列表
    async loadSparepartsHistoryList() {
        try {
            const historyResponse = await fetch(`php/get_sparepart_history.php`);
            const historyResult = await historyResponse.json();
            if (!historyResult.success) {
                throw new Error(historyResult.message);
            }
            //  
            // const response = await fetch(`php/get_sparepart_detail.php?id=${historyResult.id}`);
            // const result = await response.json();
            // if (!result.success) {
            //     throw new Error(result.message);
            // }
            
            // // 合并数据  
            // const sparepart = result.data;
            // sparepart.history = historyResult.data;
            
            // this.updateSparePartsTableHistory(historyResult.data);

            // 获取所有需要查询的备件ID
            const sparepartIds = [...new Set(historyResult.data.map(item => item.sparepart_id))];
            
            // 批量查询备件详情
            const detailPromises = sparepartIds.map(async id => {
                const response = await fetch(`php/get_sparepart_detail.php?id=${id}`);
                return response.json();
            });

            const detailResults = await Promise.all(detailPromises);
            
            // 创建备件信息映射表
            const sparepartMap = {};
            detailResults.forEach((result, index) => {
                if (result.success && result.data) {
                    sparepartMap[sparepartIds[index]] = result.data;
                }   
            });

            // 合并数据
            const mergedData = historyResult.data.map(history => {
                const detail = sparepartMap[history.sparepart_id];
                return {
                    ...history,
                    name: detail ? detail.name : '未知备件',
                    model: detail ? detail.model : '未知型号'
                };
            });
            
            //this.historyData = mergedData;
            componentContext = { 
                historyData: mergedData 
              };
            this.updateSparePartsTableHistory(mergedData);

        } catch (error) {
            console.error('加载领用履历列表失败:', error);
            alert('加载失败：' + error.message);
        }
    }
    

    // 更新领用履历表格
    updateSparePartsTableHistory(spareparts) {
        const tbody = document.querySelector('#sphistoryTab .data-table tbody');
        if (!tbody) return;

        if (!spareparts || spareparts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无领用记录</td></tr>';
            return;
        }

        tbody.innerHTML = spareparts.map(sparepart => `
            <tr>
                <td>${sparepart.name}</td>
                <td>${sparepart.model}</td>
                <td>${sparepart.action}</td>
                <td>${sparepart.quantity_change}</td>
                <td>${sparepart.stock_quantity}</td>
                <td>${sparepart.use_location}</td>
                <td>${sparepart.operator}</td>
                <td>${sparepart.change_date}</td>
            </tr>
        `).join('');
    }


    // 绑定表单事件
    bindHistoryForm() {
        document.getElementById("sphisdown").addEventListener("click", function() {
            
            
                const spareparts= componentContext.historyData;
                const ws = XLSX.utils.json_to_sheet(spareparts.map(sparepart =>  ({
                    "备品名称": sparepart.name,
                    "型号": sparepart.model,
                    "动作": sparepart.action,
                    "数量": sparepart.quantity_change,
                    "库存": sparepart.stock_quantity,
                    "操作人": sparepart.operator,
                    "日期": sparepart.change_date
                })));
    
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "备品履历");
                XLSX.writeFile(wb, `备品履历_${new Date().toLocaleDateString()}.xlsx`);

        });
        document.getElementById("sphiscopy").addEventListener("click", function() {
            // 获取表格的 HTML 内容
            var gethtml = document.getElementById("sphistory-list").outerHTML;

            // 创建一个临时的容器来复制内容
            var tempDiv = document.createElement("div");
            tempDiv.innerHTML = gethtml;
            document.body.appendChild(tempDiv);

            // 选中临时容器的内容
            var range = document.createRange();
            range.selectNodeContents(tempDiv);
            var selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // 执行复制命令
            try {
                document.execCommand("copy");
                alert("表格已复制到剪贴板！");
            } catch (error) {
                alert("复制失败：" + error.message);
            }

            // 移除临时容器
            document.body.removeChild(tempDiv);
            

    });
    }
    
    // 导出备品履历
    async exportHisData(spareparts) {
        try {
            // const queryParams = new URLSearchParams({
            //     page: 1,
            //     page_size: 0,
            //     state: this.searchParams.state,
            //     section: this.searchParams.section,
            //     key: this.searchParams.key,
            //     location: this.searchParams.location,
            //     code: this.searchParams.code
            // });

            // const response = await fetch(`php/get_spareparts.php?${queryParams.toString()}`);
            // const result = await response.json();
            
            // if (!result.success) throw new Error(result.message);
            const ws = XLSX.utils.json_to_sheet(spareparts.map(sparepart =>  ({
                "备品名称": sparepart.name,
                "型号": sparepart.model,
                "动作": sparepart.action,
                "数量": sparepart.quantity_change,
                "库存": sparepart.stock_quantity,
                "操作人": sparepart.operator,
                "日期": sparepart.change_date
            })));

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "备品履历");
            XLSX.writeFile(wb, `备品履历_${new Date().toLocaleDateString()}.xlsx`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    }

    // 格式化日期
    // formatDate(dateString) {
    //     const date = new Date(dateString);
    //     return date.toLocaleDateString('zh-CN', {
    //         year: '2-digit',
    //         month: '2-digit',
    //         day: '2-digit'
    //     });
    // }

    formatDate(dateString) {
        const date = new Date(dateString);
        return isNaN(date) 
            ? '无效日期' 
            : date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }).replace(/\//g, '-');
    }
}

// 创建实例
const noticeManager = new NoticeManager(); 